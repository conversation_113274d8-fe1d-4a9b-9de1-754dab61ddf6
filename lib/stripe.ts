import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
})

// Platform fee percentages
export const SUBSCRIPTION_FEE_PERCENTAGE = 0.20 // 20% for subscriptions
export const DONATION_FEE_PERCENTAGE = 0.05 // 5% for donations

// Calculate platform fee from total amount
export function calculatePlatformFee(totalAmount: number, paymentType: 'subscription' | 'donation' = 'subscription'): {
  platformFee: number
  writerAmount: number
  stripeFee: number
  netAfterStripe: number
} {
  // Calculate Stripe fees first (2.9% + $0.30)
  const stripeFee = Math.round(totalAmount * 0.029 + 30)
  const netAfterStripe = totalAmount - stripeFee

  // Calculate platform fee from the net amount after Stripe fees
  const feePercentage = paymentType === 'donation' ? DONATION_FEE_PERCENTAGE : SUBSCRIPTION_FEE_PERCENTAGE
  const platformFee = Math.round(netAfterStripe * feePercentage)
  const writerAmount = netAfterStripe - platformFee

  return {
    platformFee,
    writerAmount,
    stripeFee,
    netAfterStripe
  }
}

// Calculate exactly what the writer can withdraw (net amount after all fees)
export function calculateWithdrawableAmount(payments: Array<{ amount_cents: number; kind: string }>, withdrawals: Array<{ amount_cents: number }>): number {
  const totalWithdrawals = withdrawals.reduce((sum, w) => sum + w.amount_cents, 0)

  const totalNetEarnings = payments.reduce((sum, payment) => {
    // Calculate Stripe fees (2.9% + $0.30)
    const stripeFee = Math.round(payment.amount_cents * 0.029 + 30)
    const netAfterStripe = payment.amount_cents - stripeFee

    // Calculate platform fee from net amount
    const platformFeeRate = payment.kind === 'donation' ? DONATION_FEE_PERCENTAGE : SUBSCRIPTION_FEE_PERCENTAGE
    const platformFee = Math.round(netAfterStripe * platformFeeRate)

    // What the writer actually gets
    const writerAmount = netAfterStripe - platformFee

    return sum + writerAmount
  }, 0)

  return Math.max(0, totalNetEarnings - totalWithdrawals)
}

// Minimum amounts (in cents)
export const MIN_SUBSCRIPTION_AMOUNT = 299 // $2.99
export const MAX_SUBSCRIPTION_AMOUNT = 5000 // $50.00
export const MIN_DONATION_AMOUNT = 100 // $1.00
export const MAX_DONATION_AMOUNT = 10000 // $100.00

// Format price for display
export function formatPrice(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`
}
