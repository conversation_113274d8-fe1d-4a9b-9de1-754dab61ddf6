"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { BookReviewForm, BookReviewDisplay } from "@/components/BookReviewForm"
import EbookReader from "@/components/EbookReader"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  book_type: string
  price_amount: number
  average_rating: number
  review_count: number
  sales_count: number
  tags: string[]
  preview_chapters: number
  reading_time_minutes: number
  total_chapters: number
  total_words: number
  user_id: string
  author_name?: string
  users: {
    name: string
    avatar_url: string
    bio: string
  }
}

interface Review {
  id: string
  pen_rating: number
  review_title: string
  review_text: string
  helpful_count: number
  created_at: string
  users: {
    name: string
    avatar_url: string
  }
}

interface Chapter {
  id: string
  title: string
  content?: string
  chapter_number: number
  word_count: number
  is_published: boolean
}

export default function BookDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [book, setBook] = useState<Book | null>(null)
  const [reviews, setReviews] = useState<Review[]>([])
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [user, setUser] = useState<any>(null)
  const [hasPurchased, setHasPurchased] = useState(false)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [hasReviewed, setHasReviewed] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [purchasing, setPurchasing] = useState(false)
  const [shareMenuOpen, setShareMenuOpen] = useState(false)
  const [showReader, setShowReader] = useState(false)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (params.id) {
      fetchBookDetails()
      checkUser()
    }
  }, [params.id])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
    
    if (user && params.id) {
      // Check if user has purchased this book
      const { data } = await supabase
        .from('book_purchases')
        .select('id')
        .eq('user_id', user.id)
        .eq('project_id', params.id)
        .single()
      
      setHasPurchased(!!data)

      // Check if user has already reviewed this book
      if (data) {
        const { data: reviewData } = await supabase
          .from('book_reviews')
          .select('id')
          .eq('user_id', user.id)
          .eq('project_id', params.id)
          .single()

        setHasReviewed(!!reviewData)
      }
    }
  }

  const fetchBookDetails = async () => {
    try {
      console.log('=== STARTING BOOK FETCH ===')
      console.log('Fetching book details for:', params.id)
      console.log('Params object:', params)

      // Fetch book details - try by slug first, then by ID
      const baseSelect = `
        id,
        title,
        description,
        cover_image_url,
        genre,
        book_type,
        price_amount,
        average_rating,
        review_count,
        sales_count,
        tags,
        slug,
        user_id,
        created_at,
        author_name,
        is_ebook,
        is_complete,
        is_private,
        preview_chapters,
        reading_time_minutes,
        total_chapters,
        total_words,
        ebook_file_url,
        users(name, avatar, profile_picture_url, bio)
      `

      // Try to find by slug first, then fall back to ID
      console.log('Searching for book with identifier:', params.id)

      const { data: bookBySlug, error: slugError } = await supabase
        .from('projects')
        .select(baseSelect)
        .eq('is_ebook', true)
        .eq('slug', params.id)
        .single()

      let bookData
      if (bookBySlug) {
        console.log('Found book by slug:', bookBySlug.title)
        bookData = bookBySlug
      } else {
        console.log('Slug not found, trying by ID...', slugError?.message || 'No slug error details')

        const { data: bookById, error: bookError } = await supabase
          .from('projects')
          .select(baseSelect)
          .eq('is_ebook', true)
          .eq('id', params.id)
          .single()

        if (bookError) {
          console.error('Book not found by ID either:', {
            error: bookError,
            searchId: params.id,
            errorMessage: bookError?.message,
            errorCode: bookError?.code,
            errorDetails: bookError?.details
          })

          // Check if any books exist at all for debugging
          const { data: allBooks } = await supabase
            .from('projects')
            .select('id, slug, title, is_ebook')
            .eq('is_ebook', true)
            .limit(5)

          console.log('Available books for debugging:', allBooks)
          console.log('Looking for slug:', params.id)
          console.log('Available slugs:', allBooks?.map(b => b.slug))
          console.log('Available IDs:', allBooks?.map(b => b.id))

          // Also check if the book exists without the is_ebook filter
          const { data: anyBook } = await supabase
            .from('projects')
            .select('id, slug, title, is_ebook')
            .or(`id.eq.${params.id},slug.eq.${params.id}`)
            .single()

          console.log('Book exists without ebook filter:', anyBook)

          throw new Error(`Book not found with identifier: ${params.id}`)
        }
        console.log('Found book by ID:', bookById.title)
        bookData = bookById
      }

      // Ensure user data exists
      if (!bookData.users) {
        console.warn('No user data found for book, fetching separately...')
        const { data: userData } = await supabase
          .from('users')
          .select('name, avatar, profile_picture_url, bio')
          .eq('id', bookData.user_id)
          .single()

        if (userData) {
          bookData.users = userData
        }
      }

      // Add fallbacks for missing ebook fields
      const processedBookData = {
        ...bookData,
        book_type: bookData.book_type || bookData.genre || 'fiction',
        average_rating: bookData.average_rating || 0,
        review_count: bookData.review_count || 0,
        sales_count: bookData.sales_count || 0,
        tags: bookData.tags || [],
        slug: bookData.slug || bookData.title?.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') || bookData.id,
        author_name: bookData.author_name || bookData.users?.name || 'Unknown Author',
        preview_chapters: bookData.preview_chapters || 1,
        reading_time_minutes: bookData.reading_time_minutes || 30,
        total_chapters: bookData.total_chapters || 1,
        total_words: bookData.total_words || 1000
      }

      setBook(processedBookData)

      // Use the actual book ID for subsequent queries
      const bookId = bookData.id

      // Fetch reviews
      const { data: reviewsData } = await supabase
        .from('book_reviews')
        .select(`
          *,
          users(name, avatar_url)
        `)
        .eq('project_id', bookId)
        .order('created_at', { ascending: false })
        .limit(10)

      setReviews(reviewsData || [])

      // Fetch chapters for reading
      const { data: chaptersData } = await supabase
        .from('chapters')
        .select('id, title, content, chapter_number, word_count, is_published')
        .eq('project_id', bookId)
        .eq('is_published', true)
        .order('chapter_number')

      setChapters(chaptersData || [])

    } catch (error) {
      console.error('Error fetching book details:', error)
      console.error('Error details:', {
        message: error?.message,
        code: error?.code,
        details: error?.details,
        hint: error?.hint,
        params: params
      })
      setError(error?.message || 'Failed to load book details')
      // Don't redirect immediately - let user see the error
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async () => {
    if (!user) {
      router.push('/auth/login')
      return
    }

    setPurchasing(true)
    try {
      // Create Stripe checkout session
      const response = await fetch('/api/books/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookId: book?.id, // Use actual book ID, not slug
          priceAmount: book?.price_amount
        }),
      })

      const { url } = await response.json()
      if (url) {
        window.location.href = url
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
    } finally {
      setPurchasing(false)
    }
  }

  const handleShare = async (platform: string) => {
    if (!book) return

    const url = `${window.location.origin}/books/${book.slug || book.id}`
    const text = `Check out "${book.title}" by ${book.author_name || book.users.name} on OnlyDiary`

    switch (platform) {
      case 'copy':
        try {
          await navigator.clipboard.writeText(url)
          alert('Link copied to clipboard!')
        } catch (err) {
          // Fallback for older browsers
          const textArea = document.createElement('textarea')
          textArea.value = url
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          alert('Link copied to clipboard!')
        }
        break
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank')
        break
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank')
        break
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank')
        break
      case 'email':
        window.open(`mailto:?subject=${encodeURIComponent(`Check out "${book.title}"`)}&body=${encodeURIComponent(`${text}\n\n${url}`)}`)
        break
    }
    setShareMenuOpen(false)
  }

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const renderPenRating = (rating: number) => {
    const pens = Array.from({ length: 10 }, (_, i) => (
      <span key={i} className={i < rating ? "text-yellow-500" : "text-gray-300"}>
        🖊️
      </span>
    ))
    return <div className="flex">{pens}</div>
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 animate-pulse">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-1">
              <div className="aspect-[3/4] bg-gray-200 rounded-lg"></div>
            </div>
            <div className="lg:col-span-2 space-y-4">
              <div className="h-8 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Book</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="space-y-3">
            <Button onClick={() => window.location.reload()} className="w-full">
              Try Again
            </Button>
            <Link href="/books">
              <Button variant="outline" className="w-full">
                Back to Books
              </Button>
            </Link>
          </div>
          <p className="text-xs text-gray-500 mt-4">
            Check the browser console for more details
          </p>
        </div>
      </div>
    )
  }

  if (!book) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📚</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">Book not found</h2>
          <p className="text-gray-600 mb-4">The book you're looking for doesn't exist or has been removed.</p>
          <Link href="/books">
            <Button>Browse Books</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">

        {/* Back Button */}
        <div className="mb-4 sm:mb-6">
          <Link href="/books" className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors text-sm sm:text-base">
            ← Back to Books
          </Link>
        </div>

        {/* Book Details */}
        <div className="grid lg:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12">

          {/* Book Cover */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-24">
              <div className="aspect-[3/4] relative overflow-hidden rounded-lg bg-gradient-to-br from-purple-100 to-blue-100 shadow-lg max-w-xs mx-auto lg:max-w-none">
                {book.cover_image_url ? (
                  <img
                    src={book.cover_image_url}
                    alt={book.title}
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-4xl sm:text-6xl">
                    📖
                  </div>
                )}
              </div>

              {/* Purchase/Read Button */}
              <div className="mt-4 sm:mt-6 space-y-3">
                {book.price_amount === 0 || hasPurchased ? (
                  <Button
                    className="w-full"
                    size="lg"
                    onClick={() => setShowReader(true)}
                    disabled={chapters.length === 0}
                  >
                    {chapters.length === 0
                      ? "📚 Processing..."
                      : hasPurchased
                        ? "📖 Continue Reading"
                        : "📖 Read Free"
                    }
                  </Button>
                ) : (
                  <Button 
                    onClick={handlePurchase}
                    isLoading={purchasing}
                    className="w-full" 
                    size="lg"
                  >
                    💰 Buy for {formatPrice(book.price_amount)}
                  </Button>
                )}

                {!hasPurchased && book.price_amount > 0 && chapters.length > 0 && (
                  <Link href={`/books/${book.id}/preview`} className="w-full">
                    <Button variant="outline" className="w-full">
                      👀 Preview ({book.preview_chapters} chapters)
                    </Button>
                  </Link>
                )}

                {/* Share Button */}
                <div className="relative">
                  <Button
                    variant="outline"
                    onClick={() => setShareMenuOpen(!shareMenuOpen)}
                    className="w-full"
                  >
                    🔗 Share Book
                  </Button>

                  {shareMenuOpen && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-20">
                      <button
                        onClick={() => handleShare('copy')}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        📋 Copy Link
                      </button>
                      <button
                        onClick={() => handleShare('twitter')}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        🐦 Share on Twitter
                      </button>
                      <button
                        onClick={() => handleShare('facebook')}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        📘 Share on Facebook
                      </button>
                      <button
                        onClick={() => handleShare('email')}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        ✉️ Share via Email
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Book Stats */}
              <div className="mt-6 space-y-3 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Pages:</span>
                  <span>~{Math.ceil(book.total_words / 250)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Reading time:</span>
                  <span>{Math.ceil(book.reading_time_minutes || book.total_words / 200)} min</span>
                </div>
                <div className="flex justify-between">
                  <span>Chapters:</span>
                  <span>{book.total_chapters}</span>
                </div>
                <div className="flex justify-between">
                  <span>Sales:</span>
                  <span>{book.sales_count} copies</span>
                </div>
              </div>
            </div>
          </div>

          {/* Book Info */}
          <div className="lg:col-span-2">
            <div className="space-y-6">
              
              {/* Title & Author */}
              <div>
                <h1 className="text-3xl sm:text-4xl font-serif text-gray-900 mb-2">
                  {book.title}
                </h1>
                <Link 
                  href={`/profile/${book.user_id}`}
                  className="inline-flex items-center gap-2 text-lg text-gray-600 hover:text-purple-600 transition-colors"
                >
                  {book.users?.avatar_url && (
                    <img 
                      src={book.users.avatar_url} 
                      alt={book.users.name}
                      className="w-6 h-6 rounded-full"
                    />
                  )}
                  by {book.author_name || book.users?.name || 'Anonymous'}
                </Link>
              </div>

              {/* Rating */}
              {book.average_rating > 0 && (
                <div className="flex items-center gap-4">
                  {renderPenRating(Math.round(book.average_rating))}
                  <span className="text-lg font-medium">
                    {book.average_rating.toFixed(1)}/10
                  </span>
                  <span className="text-gray-600">
                    ({book.review_count} reviews)
                  </span>
                </div>
              )}

              {/* Description */}
              {book.description && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">About this book</h3>
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                    {book.description}
                  </p>
                </div>
              )}

              {/* Tags & Genre */}
              <div className="flex flex-wrap gap-2">
                {book.genre && (
                  <span className="inline-block px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full">
                    {book.genre.replace('_', ' ')}
                  </span>
                )}
                {book.book_type && book.book_type !== book.genre && (
                  <span className="inline-block px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">
                    {book.book_type.replace('_', ' ')}
                  </span>
                )}
                {book.tags?.map((tag, index) => (
                  <span key={index} className="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">
                    {tag}
                  </span>
                ))}
              </div>

              {/* Author Bio */}
              {book.users?.bio && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">About the author</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {book.users.bio}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Reviews Section */}
        <div className="border-t border-gray-200 pt-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-serif text-gray-900">
              Reader Reviews ({reviews.length})
            </h2>

            {/* Review Button for Verified Purchasers */}
            {user && hasPurchased && !hasReviewed && !showReviewForm && (
              <Button
                onClick={() => setShowReviewForm(true)}
                className="bg-purple-600 text-white hover:bg-purple-700"
              >
                📝 Write Review
              </Button>
            )}
          </div>

          {/* Review Form */}
          {showReviewForm && user && (
            <div className="mb-8">
              <BookReviewForm
                bookId={params.id as string}
                userId={user.id}
                onReviewSubmitted={() => {
                  setShowReviewForm(false)
                  setHasReviewed(true)
                  fetchBookDetails() // Refresh reviews
                }}
                onCancel={() => setShowReviewForm(false)}
              />
            </div>
          )}

          {/* Reviews Display */}
          <BookReviewDisplay
            reviews={reviews}
            averageRating={book?.average_rating || 0}
            totalReviews={reviews.length}
          />
        </div>
      </div>

      {/* Ebook Reader Modal */}
      {showReader && chapters.length > 0 && (
        <EbookReader
          chapters={chapters.map(chapter => ({
            id: chapter.id,
            title: chapter.title,
            content: chapter.content || 'Chapter content not available.',
            chapter_number: chapter.chapter_number,
            word_count: chapter.word_count
          }))}
          bookTitle={book?.title || 'Unknown Title'}
          authorName={book?.author_name || 'Unknown Author'}
          onClose={() => setShowReader(false)}
        />
      )}
    </div>
  )
}
