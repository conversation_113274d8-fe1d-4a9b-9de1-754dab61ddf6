import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { processEbook } from '@/lib/ebook-processor'

export async function POST(request: NextRequest) {
  try {
    const { projectId, fileUrl, fileType } = await request.json()

    if (!projectId || !fileUrl || !fileType) {
      return NextResponse.json(
        { error: 'Project ID, file URL, and file type are required' },
        { status: 400 }
      )
    }

    if (!['pdf', 'epub'].includes(fileType)) {
      return NextResponse.json(
        { error: 'File type must be pdf or epub' },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id, ebook_file_type, author_name, meta_description')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    try {
      console.log(`Processing ${fileType.toUpperCase()} file for project:`, projectId)
      
      // Process the ebook file using our robust processor
      const result = await processEbook(fileUrl, fileType as 'pdf' | 'epub')
      
      console.log('Processing completed:', {
        chapters: result.chapters.length,
        wordCount: result.wordCount,
        pageCount: result.pageCount,
        readingTime: result.readingTimeMinutes,
        readabilityScore: result.readabilityScore
      })
      
      // Prepare update data with extracted metadata
      const updateData: any = {
        total_chapters: result.chapters.length,
        total_words: result.wordCount,
        reading_time_minutes: result.readingTimeMinutes,
        is_complete: true
      }

      // Update metadata if extracted and not already set
      if (result.metadata.author && !project.author_name) {
        updateData.author_name = result.metadata.author
      }
      
      if (result.suggestedTags.length > 0) {
        updateData.tags = result.suggestedTags
      }
      
      if (result.description && !project.meta_description) {
        updateData.meta_description = result.description.substring(0, 300)
      }

      // Update EPUB-specific metadata
      if (fileType === 'epub' && result.metadata) {
        if (result.metadata.isbn && !updateData.isbn) {
          updateData.isbn = result.metadata.isbn
        }
        if (result.metadata.publishedDate && !updateData.publication_date) {
          updateData.publication_date = result.metadata.publishedDate
        }
        if (result.metadata.publisher && !updateData.publisher) {
          updateData.publisher = result.metadata.publisher
        }
        if (result.metadata.language && !updateData.language) {
          updateData.language = result.metadata.language
        }
      }

      const { error: updateError } = await supabase
        .from('projects')
        .update(updateData)
        .eq('id', projectId)

      if (updateError) {
        console.error('Error updating project stats:', updateError)
        throw new Error('Failed to update project')
      }

      // Clear existing chapters before creating new ones
      if (result.chapters.length > 0) {
        console.log('Clearing existing chapters...')
        await supabase
          .from('chapters')
          .delete()
          .eq('project_id', projectId)

        console.log(`Creating ${result.chapters.length} chapters...`)
        
        const chaptersToInsert = result.chapters.map((chapter, index) => ({
          project_id: projectId,
          user_id: user.id,
          title: chapter.title,
          content: chapter.content,
          chapter_number: index + 1,
          word_count: chapter.wordCount,
          is_published: true
        }))

        const { error: chaptersError } = await supabase
          .from('chapters')
          .insert(chaptersToInsert)

        if (chaptersError) {
          console.error('Error creating chapters:', chaptersError)
          // Don't fail the whole process, just log the error
        } else {
          console.log('Chapters created successfully')
        }
      }

      return NextResponse.json({
        success: true,
        message: `${fileType.toUpperCase()} processed successfully`,
        data: {
          chaptersCreated: result.chapters.length,
          totalWords: result.wordCount,
          readingTime: result.readingTimeMinutes,
          pageCount: result.pageCount,
          suggestedTags: result.suggestedTags,
          readabilityScore: result.readabilityScore,
          metadata: result.metadata,
          extractedDescription: result.description
        }
      })

    } catch (processingError) {
      console.error('Ebook processing error:', processingError)
      return NextResponse.json(
        { error: `Failed to process ${fileType}: ${processingError.message}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
