"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface BookReviewFormProps {
  bookId: string
  userId: string
  onReviewSubmitted: () => void
  onCancel: () => void
}

export function BookReviewForm({ bookId, userId, onReviewSubmitted, onCancel }: BookReviewFormProps) {
  const [rating, setRating] = useState(0)
  const [reviewText, setReviewText] = useState("")
  const [submitting, setSubmitting] = useState(false)
  const supabase = createSupabaseClient()

  const handleSubmit = async () => {
    if (rating === 0) {
      alert("Please select a rating")
      return
    }

    setSubmitting(true)
    try {
      const { error } = await supabase
        .from('book_reviews')
        .insert({
          user_id: userId,
          project_id: bookId,
          rating: rating,
          review_text: reviewText.trim() || null
        })

      if (error) throw error

      onReviewSubmitted()
    } catch (error) {
      console.error('Error submitting review:', error)
      alert('Failed to submit review. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Card className="border-purple-200 bg-purple-50/50">
      <CardContent className="p-6">
        <h3 className="text-lg font-serif text-gray-900 mb-4">
          📝 Write a Review
        </h3>
        
        <div className="space-y-4">
          {/* 10-Pen Rating System */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Rating (1-10 pens)
            </label>
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((pen) => (
                <button
                  key={pen}
                  onClick={() => setRating(pen)}
                  className={`text-2xl transition-all hover:scale-110 ${
                    pen <= rating 
                      ? 'text-purple-600 drop-shadow-sm' 
                      : 'text-gray-300 hover:text-purple-400'
                  }`}
                  type="button"
                >
                  🖊️
                </button>
              ))}
              <span className="ml-2 text-sm text-gray-600">
                {rating > 0 ? `${rating}/10 pens` : 'Select rating'}
              </span>
            </div>
          </div>

          {/* Review Text */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Review (optional)
            </label>
            <textarea
              value={reviewText}
              onChange={(e) => setReviewText(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-24 resize-none"
              placeholder="Share your thoughts about this book..."
              maxLength={1000}
            />
            <p className="text-xs text-gray-500 mt-1">
              {reviewText.length}/1000 characters
            </p>
          </div>

          {/* Verified Purchase Notice */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center gap-2 text-green-800">
              <span className="text-sm">✅</span>
              <span className="text-sm font-medium">Verified Purchase</span>
            </div>
            <p className="text-xs text-green-700 mt-1">
              Only customers who have purchased this book can leave reviews
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              onClick={onCancel}
              variant="outline"
              className="flex-1"
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              isLoading={submitting}
              disabled={rating === 0}
              className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
            >
              {submitting ? 'Submitting...' : 'Submit Review'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface BookReviewDisplayProps {
  reviews: Array<{
    id: string
    rating: number
    review_text: string | null
    created_at: string
    users: {
      name: string
      avatar_url: string | null
    }
  }>
  averageRating: number
  totalReviews: number
}

export function BookReviewDisplay({ reviews, averageRating, totalReviews }: BookReviewDisplayProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const renderPenRating = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((pen) => (
          <span
            key={pen}
            className={`text-sm ${
              pen <= rating ? 'text-purple-600' : 'text-gray-300'
            }`}
          >
            🖊️
          </span>
        ))}
        <span className="ml-2 text-sm font-medium text-gray-700">
          {rating}/10
        </span>
      </div>
    )
  }

  if (reviews.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-3">📝</div>
        <h3 className="text-lg font-serif text-gray-800 mb-2">No reviews yet</h3>
        <p className="text-gray-600">Be the first to review this book!</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2 mb-1">
              {renderPenRating(Math.round(averageRating))}
            </div>
            <p className="text-sm text-gray-600">
              Average of {totalReviews} review{totalReviews !== 1 ? 's' : ''}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-purple-600">
              {averageRating.toFixed(1)}
            </div>
            <div className="text-xs text-gray-500">out of 10</div>
          </div>
        </div>
      </div>

      {/* Individual Reviews */}
      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id} className="border-gray-200">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                  {review.users.avatar_url ? (
                    <img
                      src={review.users.avatar_url}
                      alt={review.users.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-purple-600 font-medium">
                      {review.users.name.charAt(0).toUpperCase()}
                    </span>
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {review.users.name}
                      </h4>
                      <div className="flex items-center gap-2">
                        {renderPenRating(review.rating)}
                        <span className="text-xs text-green-600">✅ Verified Purchase</span>
                      </div>
                    </div>
                    <time className="text-xs text-gray-500">
                      {formatDate(review.created_at)}
                    </time>
                  </div>
                  
                  {review.review_text && (
                    <p className="text-gray-700 leading-relaxed">
                      {review.review_text}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
