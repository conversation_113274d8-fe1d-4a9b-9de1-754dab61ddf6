"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
// Client-safe price formatting function
function formatPrice(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`
}

interface PaymentButtonProps {
  writerId: string
  writerName: string
  subscriptionPrice?: number
  acceptsDonations?: boolean
  monetizationType: 'subscription' | 'donations' | 'both'
  className?: string
}

export function PaymentButton({ 
  writerId, 
  writerName, 
  subscriptionPrice, 
  acceptsDonations,
  monetizationType,
  className = ""
}: PaymentButtonProps) {
  const [loading, setLoading] = useState(false)
  const [showDonationInput, setShowDonationInput] = useState(false)
  const [donationAmount, setDonationAmount] = useState("")
  const [donationMessage, setDonationMessage] = useState("")

  const handleSubscribe = async () => {
    setLoading(true)
    
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          writerId,
          paymentType: 'subscription'
        })
      })

      const data = await response.json()

      if (data.url) {
        window.location.href = data.url
      } else {
        console.error('No checkout URL returned')
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDonate = async () => {
    const amount = Math.round(parseFloat(donationAmount) * 100) // Convert to cents
    
    if (amount < 100) {
      alert('Minimum donation is $1.00')
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          writerId,
          paymentType: 'donation',
          amount,
          message: donationMessage
        })
      })

      const data = await response.json()

      if (data.url) {
        window.location.href = data.url
      } else {
        console.error('No checkout URL returned')
      }
    } catch (error) {
      console.error('Error creating donation checkout:', error)
    } finally {
      setLoading(false)
    }
  }

  // Donations only
  if (monetizationType === 'donations') {
    return (
      <div className={`space-y-4 ${className}`}>
        {!showDonationInput ? (
          <Button
            onClick={() => setShowDonationInput(true)}
            className="w-full bg-green-600 text-white hover:bg-green-700"
          >
            💝 Support {writerName}
          </Button>
        ) : (
          <div className="space-y-3 p-4 border border-gray-200 rounded-lg">
            <h4 className="font-medium text-gray-900">Support {writerName}</h4>
            
            <div>
              <label className="block text-sm text-gray-600 mb-1">Amount</label>
              <div className="flex items-center">
                <span className="text-gray-500 mr-2">$</span>
                <input
                  type="number"
                  min="1"
                  step="0.01"
                  value={donationAmount}
                  onChange={(e) => setDonationAmount(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="5.00"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm text-gray-600 mb-1">Message (optional)</label>
              <textarea
                value={donationMessage}
                onChange={(e) => setDonationMessage(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Thank you for your writing!"
                rows={2}
                maxLength={200}
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleDonate}
                isLoading={loading}
                disabled={!donationAmount || parseFloat(donationAmount) < 1}
                className="flex-1 bg-green-600 text-white hover:bg-green-700"
              >
                Donate ${donationAmount || '0.00'}
              </Button>
              <Button
                onClick={() => setShowDonationInput(false)}
                variant="secondary"
                className="px-4"
              >
                Cancel
              </Button>
            </div>

            <p className="text-xs text-gray-400 mt-2">
              Stripe charges processing fees (~2.9% + 30¢) in addition to our 5% platform fee.
            </p>
          </div>
        )}
      </div>
    )
  }

  // Subscription only
  if (monetizationType === 'subscription') {
    return (
      <div className={className}>
        <Button
          onClick={handleSubscribe}
          isLoading={loading}
          className="w-full bg-blue-600 text-white hover:bg-blue-700"
        >
          Subscribe for {subscriptionPrice ? formatPrice(subscriptionPrice) : '$9.99'}
        </Button>
      </div>
    )
  }

  // Both subscription and donations
  return (
    <div className={`space-y-3 ${className}`}>
      <div>
        <Button
          onClick={handleSubscribe}
          isLoading={loading}
          className="w-full bg-blue-600 text-white hover:bg-blue-700"
        >
          Subscribe for {subscriptionPrice ? formatPrice(subscriptionPrice) : '$9.99'}
        </Button>
      </div>

      <div className="text-center text-sm text-gray-500">or</div>
      
      {!showDonationInput ? (
        <Button
          onClick={() => setShowDonationInput(true)}
          variant="secondary"
          className="w-full"
        >
          💝 Make a Donation
        </Button>
      ) : (
        <div className="space-y-3 p-4 border border-gray-200 rounded-lg">
          <h4 className="font-medium text-gray-900">Support {writerName}</h4>
          
          <div>
            <label className="block text-sm text-gray-600 mb-1">Amount</label>
            <div className="flex items-center">
              <span className="text-gray-500 mr-2">$</span>
              <input
                type="number"
                min="1"
                step="0.01"
                value={donationAmount}
                onChange={(e) => setDonationAmount(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="5.00"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm text-gray-600 mb-1">Message (optional)</label>
            <textarea
              value={donationMessage}
              onChange={(e) => setDonationMessage(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Thank you for your writing!"
              rows={2}
              maxLength={200}
            />
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleDonate}
              isLoading={loading}
              disabled={!donationAmount || parseFloat(donationAmount) < 1}
              className="flex-1 bg-green-600 text-white hover:bg-green-700"
            >
              Donate ${donationAmount || '0.00'}
            </Button>
            <Button
              onClick={() => setShowDonationInput(false)}
              variant="secondary"
              className="px-4"
            >
              Cancel
            </Button>
          </div>

          <p className="text-xs text-gray-400 mt-2">
            Stripe charges processing fees (~2.9% + 30¢) in addition to our 5% platform fee.
          </p>
        </div>
      )}
    </div>
  )
}
