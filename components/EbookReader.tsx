"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, BookOpen, Settings, Bookmark, Search } from "lucide-react"

interface Chapter {
  id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
}

interface EbookReaderProps {
  chapters: Chapter[]
  bookTitle: string
  authorName: string
  onClose: () => void
}

export function EbookReader({ chapters, bookTitle, authorName, onClose }: EbookReaderProps) {
  const [currentChapter, setCurrentChapter] = useState(0)
  const [fontSize, setFontSize] = useState(16)
  const [fontFamily, setFontFamily] = useState('serif')
  const [lineHeight, setLineHeight] = useState(1.6)
  const [theme, setTheme] = useState('light')
  const [showSettings, setShowSettings] = useState(false)
  const [showChapterList, setShowChapterList] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [bookmarks, setBookmarks] = useState<number[]>([])

  // Calculate reading progress
  useEffect(() => {
    const totalChapters = chapters.length
    const progress = totalChapters > 0 ? ((currentChapter + 1) / totalChapters) * 100 : 0
    setReadingProgress(progress)
  }, [currentChapter, chapters.length])

  const nextChapter = () => {
    if (currentChapter < chapters.length - 1) {
      setCurrentChapter(currentChapter + 1)
    }
  }

  const prevChapter = () => {
    if (currentChapter > 0) {
      setCurrentChapter(currentChapter - 1)
    }
  }

  const toggleBookmark = () => {
    if (bookmarks.includes(currentChapter)) {
      setBookmarks(bookmarks.filter(b => b !== currentChapter))
    } else {
      setBookmarks([...bookmarks, currentChapter])
    }
  }

  const getThemeClasses = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gray-900 text-gray-100'
      case 'sepia':
        return 'bg-yellow-50 text-yellow-900'
      default:
        return 'bg-white text-gray-900'
    }
  }

  const currentChapterData = chapters[currentChapter]

  if (!currentChapterData) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <div className="text-red-500 text-4xl mb-4">📚</div>
            <h3 className="text-lg font-semibold mb-2">No Chapters Available</h3>
            <p className="text-gray-600 mb-4">This book doesn't have any readable chapters yet.</p>
            <Button onClick={onClose}>Close Reader</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`fixed inset-0 z-50 ${getThemeClasses()}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={onClose}>
            <ChevronLeft className="h-4 w-4 mr-1" />
            Close
          </Button>
          <div>
            <h1 className="font-semibold text-lg">{bookTitle}</h1>
            <p className="text-sm opacity-70">by {authorName}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={toggleBookmark}
            className={bookmarks.includes(currentChapter) ? 'text-yellow-500' : ''}
          >
            <Bookmark className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={() => setShowChapterList(!showChapterList)}>
            <BookOpen className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)}>
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="h-1 bg-gray-200 dark:bg-gray-700">
        <div 
          className="h-full bg-blue-600 transition-all duration-300"
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      <div className="flex h-full">
        {/* Chapter List Sidebar */}
        {showChapterList && (
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold mb-4">Chapters</h3>
              <div className="space-y-2">
                {chapters.map((chapter, index) => (
                  <button
                    key={chapter.id}
                    onClick={() => {
                      setCurrentChapter(index)
                      setShowChapterList(false)
                    }}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      index === currentChapter 
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100' 
                        : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                    }`}
                  >
                    <div className="font-medium text-sm">
                      Chapter {chapter.chapter_number}
                    </div>
                    <div className="text-xs opacity-70 mt-1">
                      {chapter.title}
                    </div>
                    <div className="text-xs opacity-50 mt-1">
                      {chapter.word_count.toLocaleString()} words
                    </div>
                    {bookmarks.includes(index) && (
                      <div className="text-yellow-500 text-xs mt-1">
                        🔖 Bookmarked
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Settings Sidebar */}
        {showSettings && (
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold mb-4">Reading Settings</h3>
              
              <div className="space-y-6">
                {/* Font Size */}
                <div>
                  <label className="block text-sm font-medium mb-2">Font Size</label>
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                    >
                      A-
                    </Button>
                    <span className="text-sm">{fontSize}px</span>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                    >
                      A+
                    </Button>
                  </div>
                </div>

                {/* Font Family */}
                <div>
                  <label className="block text-sm font-medium mb-2">Font Family</label>
                  <select 
                    value={fontFamily}
                    onChange={(e) => setFontFamily(e.target.value)}
                    className="w-full p-2 border rounded-md bg-background"
                  >
                    <option value="serif">Serif</option>
                    <option value="sans-serif">Sans Serif</option>
                    <option value="monospace">Monospace</option>
                  </select>
                </div>

                {/* Line Height */}
                <div>
                  <label className="block text-sm font-medium mb-2">Line Height</label>
                  <input
                    type="range"
                    min="1.2"
                    max="2.0"
                    step="0.1"
                    value={lineHeight}
                    onChange={(e) => setLineHeight(parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <span className="text-sm">{lineHeight}</span>
                </div>

                {/* Theme */}
                <div>
                  <label className="block text-sm font-medium mb-2">Theme</label>
                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant={theme === 'light' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('light')}
                    >
                      Light
                    </Button>
                    <Button
                      variant={theme === 'dark' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('dark')}
                    >
                      Dark
                    </Button>
                    <Button
                      variant={theme === 'sepia' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('sepia')}
                    >
                      Sepia
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Reading Area */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 overflow-y-auto">
            <div className="max-w-4xl mx-auto p-8">
              <div className="mb-8">
                <h2 
                  className="text-2xl font-bold mb-2"
                  style={{ 
                    fontSize: `${fontSize + 8}px`,
                    fontFamily,
                    lineHeight 
                  }}
                >
                  {currentChapterData.title}
                </h2>
                <div className="text-sm opacity-70">
                  Chapter {currentChapterData.chapter_number} • {currentChapterData.word_count.toLocaleString()} words
                </div>
              </div>
              
              <div 
                className="prose prose-lg max-w-none"
                style={{ 
                  fontSize: `${fontSize}px`,
                  fontFamily,
                  lineHeight 
                }}
              >
                {currentChapterData.content.split('\n').map((paragraph, index) => (
                  <p key={index} className="mb-4">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          </div>

          {/* Navigation Footer */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between max-w-4xl mx-auto">
              <Button 
                variant="outline"
                onClick={prevChapter}
                disabled={currentChapter === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              
              <div className="text-sm opacity-70">
                {currentChapter + 1} of {chapters.length}
              </div>
              
              <Button 
                variant="outline"
                onClick={nextChapter}
                disabled={currentChapter === chapters.length - 1}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EbookReader
